# PDF Generator with Node.js and Puppeteer

A powerful and flexible PDF generation library built with Node.js and Puppeteer. This library provides easy-to-use functions for converting HTML content to high-quality PDF documents.

## Features

- 🚀 **Fast PDF Generation**: Efficient HTML to PDF conversion using Puppeteer
- 🎨 **Custom Styling**: Full CSS support with optimized styles for PDF output
- 📄 **Multiple Input Types**: Generate PDFs from HTML strings or URLs
- ⚙️ **Configurable Options**: Customizable page formats, margins, headers, and footers
- 📋 **Resume Templates**: Pre-built optimized styling for professional resumes
- 🔧 **Singleton Pattern**: Efficient browser resource management
- 💾 **Buffer Output**: Returns PDF as Buffer for flexible handling

## Installation

1. Clone or download this repository
2. Install dependencies:

```bash
npm install
```

## Dependencies

- **puppeteer**: ^24.22.0 - For browser automation and PDF generation

## Quick Start

### Basic Usage

```javascript
const { pdfGenerator } = require('./index');

async function generatePdf() {
  const htmlContent = '<h1>Hello World</h1><p>This is a test PDF.</p>';
  
  try {
    const pdfBuffer = await pdfGenerator.generatePdfFromHtml(htmlContent);
    
    // Save to file
    const fs = require('fs');
    fs.writeFileSync('output.pdf', pdfBuffer);
    
    console.log('PDF generated successfully!');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pdfGenerator.close();
  }
}

generatePdf();
```

### Generate PDF from URL

```javascript
const { pdfGenerator } = require('./index');

async function generateFromUrl() {
  try {
    const pdfBuffer = await pdfGenerator.generatePdfFromUrl('https://example.com');
    
    const fs = require('fs');
    fs.writeFileSync('webpage.pdf', pdfBuffer);
    
    console.log('PDF from URL generated successfully!');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pdfGenerator.close();
  }
}
```

### Professional Resume PDF with Optimized Styling

```javascript
const { generateResumePdf } = require('./index');

async function createResume() {
  const resumeHtml = `
    <div class="contact-info">
      <h1>Jane Smith</h1>
      <div class="contact-details">
        <span><EMAIL></span>
        <span>+1 (555) 987-6543</span>
        <span>LinkedIn: linkedin.com/in/janesmith</span>
        <span>San Francisco, CA</span>
      </div>
    </div>

    <div class="section">
      <h2>Professional Summary</h2>
      <p>Senior Full-Stack Developer with 8+ years of experience building scalable web applications.</p>
    </div>

    <div class="section">
      <h2>Technical Skills</h2>
      <div class="skills-grid">
        <div class="skill-category">
          <h4>Frontend</h4>
          <div class="skill-tags">
            <span class="skill-tag">React</span>
            <span class="skill-tag">TypeScript</span>
            <span class="skill-tag">Vue.js</span>
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>Professional Experience</h2>
      <div class="experience-item">
        <div class="item-header">
          <div>
            <div class="item-title">Senior Full-Stack Developer</div>
            <div class="item-company">TechCorp Solutions</div>
            <div class="item-location">San Francisco, CA</div>
          </div>
          <div class="item-date">Mar 2020 - Present</div>
        </div>
        <div class="achievements">
          <ul>
            <li>Led development of microservices architecture serving 2M+ users</li>
            <li>Reduced application load time by 60% through performance optimization</li>
          </ul>
        </div>
      </div>
    </div>
  `;

  try {
    const pdfBuffer = await generateResumePdf(resumeHtml);

    const fs = require('fs');
    fs.writeFileSync('resume.pdf', pdfBuffer);

    console.log('Resume PDF generated successfully!');
  } catch (error) {
    console.error('Error:', error);
  }
}
```

## Configuration Options

### PDF Generation Options

```javascript
const options = {
  format: 'A4',           // 'A4', 'Letter', etc.
  margin: {
    top: '0.5in',
    right: '0.5in',
    bottom: '0.5in',
    left: '0.5in'
  },
  printBackground: true,   // Include background colors/images
  displayHeaderFooter: false,
  headerTemplate: '<div>Header content</div>',
  footerTemplate: '<div>Footer content</div>'
};

const pdfBuffer = await pdfGenerator.generatePdfFromHtml(htmlContent, options);
```

## Running Examples

### Run All Sample Functions

```bash
npm run sample
```

This will generate multiple PDF examples demonstrating different features and use cases.

### Run Resume Example

```bash
npm run examples
```

This will generate a professional resume PDF example demonstrating optimized styling and formatting.

## API Reference

### PdfGenerator Class

#### Methods

- `generatePdfFromHtml(htmlContent, options)` - Generate PDF from HTML string
- `generatePdfFromUrl(url, options)` - Generate PDF from URL
- `close()` - Close browser and clean up resources

#### Singleton Instance

```javascript
const { pdfGenerator } = require('./index');
// Use pdfGenerator instance directly
```

### Utility Functions

- `generateResumePdf(htmlContent)` - Generate optimized resume PDF

## CSS Classes for Resume Styling

The library includes pre-built CSS classes for professional resume formatting:

- `.contact-info` - Header section with contact details
- `.section` - Main content sections
- `.experience-item` - Individual experience entries
- `.education-item` - Education entries
- `.skills-grid` - Skills section layout
- `.skill-tag` - Individual skill tags

## Browser Configuration

The library uses optimized Puppeteer settings for reliable PDF generation:

- Headless mode enabled
- Optimized Chrome flags for stability
- Proper resource cleanup
- Error handling and recovery

## Troubleshooting

### Common Issues

1. **Browser launch fails**: Ensure you have sufficient system resources
2. **PDF generation timeout**: Increase timeout values in options
3. **Missing fonts**: Install required fonts on your system
4. **Memory issues**: Call `pdfGenerator.close()` to clean up resources

### Performance Tips

- Reuse the same PdfGenerator instance for multiple PDFs
- Use `domcontentloaded` instead of `networkidle0` for faster generation
- Optimize CSS for print media
- Close browser when done to free resources

## License

ISC License

## Contributing

Feel free to submit issues and enhancement requests!
