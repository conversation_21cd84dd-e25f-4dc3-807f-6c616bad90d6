const fs = require('fs');
const { generateResumePdf } = require('./index');

// Professional Resume Example
async function resumeExample() {
  console.log('👔 Example 3: Professional Resume');
  
  const resumeHtml = `
    <div class="contact-info">
      <h1><PERSON></h1>
      <div class="contact-details">
        <span><EMAIL></span>
        <span>+1 (555) 987-6543</span>
        <span>LinkedIn: linkedin.com/in/janesmith</span>
        <span>San Francisco, CA</span>
      </div>
    </div>

    <div class="section">
      <h2>Professional Summary</h2>
      <p>Senior Full-Stack Developer with 8+ years of experience building scalable web applications. 
      Expert in React, Node.js, and cloud technologies with a proven track record of leading 
      high-performing development teams.</p>
    </div>

    <div class="section">
      <h2>Technical Skills</h2>
      <div class="skills-grid">
        <div class="skill-category">
          <h4>Frontend</h4>
          <div class="skill-tags">
            <span class="skill-tag">React</span>
            <span class="skill-tag">Angular</span>
            <span class="skill-tag">Vue.js</span>
            <span class="skill-tag">TypeScript</span>
          </div>
        </div>
        <div class="skill-category">
          <h4>Backend</h4>
          <div class="skill-tags">
            <span class="skill-tag">Node.js</span>
            <span class="skill-tag">Python</span>
            <span class="skill-tag">Java</span>
            <span class="skill-tag">PostgreSQL</span>
          </div>
        </div>
        <div class="skill-category">
          <h4>Cloud</h4>
          <div class="skill-tags">
            <span class="skill-tag">AWS</span>
            <span class="skill-tag">Docker</span>
            <span class="skill-tag">Kubernetes</span>
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>Professional Experience</h2>
      
      <div class="experience-item">
        <div class="item-header">
          <div>
            <div class="item-title">Senior Full-Stack Developer</div>
            <div class="item-company">TechCorp Solutions</div>
            <div class="item-location">San Francisco, CA</div>
          </div>
          <div class="item-date">Mar 2020 - Present</div>
        </div>
        <div class="achievements">
          <ul>
            <li>Led development of microservices architecture serving 2M+ users</li>
            <li>Reduced application load time by 60% through performance optimization</li>
            <li>Mentored team of 5 junior developers and established code review processes</li>
            <li>Implemented CI/CD pipelines reducing deployment time from hours to minutes</li>
          </ul>
        </div>
      </div>

      <div class="experience-item">
        <div class="item-header">
          <div>
            <div class="item-title">Full-Stack Developer</div>
            <div class="item-company">InnovateLab</div>
            <div class="item-location">Palo Alto, CA</div>
          </div>
          <div class="item-date">Jun 2018 - Feb 2020</div>
        </div>
        <div class="achievements">
          <ul>
            <li>Built responsive web applications using React and Node.js</li>
            <li>Designed and implemented RESTful APIs serving mobile and web clients</li>
            <li>Collaborated with UX team to improve user engagement by 40%</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>Education</h2>
      
      <div class="education-item">
        <div class="item-header">
          <div>
            <div class="item-title">Master of Science in Computer Science</div>
            <div class="item-company">Stanford University</div>
            <div class="item-location">Stanford, CA</div>
          </div>
          <div class="item-date">2016 - 2018</div>
        </div>
        <p>Specialization in Artificial Intelligence and Machine Learning</p>
      </div>
    </div>
  `;
  
  try {
    const pdfBuffer = await generateResumePdf(resumeHtml);
    fs.writeFileSync('example-resume.pdf', pdfBuffer);
    console.log('✅ Resume PDF saved as example-resume.pdf');
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Run resume example
async function runResumeExample() {
  console.log('👔 Running Resume PDF Generation Example\n');

  try {
    await resumeExample();
    console.log('');

    console.log('🎉 Resume example completed successfully!');
    console.log('📁 Check the generated PDF file in the current directory.');

  } catch (error) {
    console.error('💥 Example execution failed:', error);
  }
}

// Export functions
module.exports = {
  resumeExample,
  runResumeExample
};

// Run resume example if this file is executed directly
if (require.main === module) {
  runResumeExample();
}
