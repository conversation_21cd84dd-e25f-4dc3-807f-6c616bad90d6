const fs = require('fs');
const { pdfGenerator, generateResumePdf } = require('./index');

// Example 1: Basic HTML to PDF
async function basicExample() {
  console.log('📝 Example 1: Basic HTML to PDF');
  
  const html = `
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          h1 { color: #333; }
          .highlight { background-color: #ffeb3b; padding: 10px; }
        </style>
      </head>
      <body>
        <h1>My First PDF</h1>
        <p>This is a simple HTML to PDF conversion example.</p>
        <div class="highlight">
          <strong>Note:</strong> This PDF was generated on ${new Date().toLocaleDateString()}
        </div>
      </body>
    </html>
  `;
  
  try {
    const pdfBuffer = await pdfGenerator.generatePdfFromHtml(html);
    fs.writeFileSync('example-basic.pdf', pdfBuffer);
    console.log('✅ Basic PDF saved as example-basic.pdf');
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Example 2: PDF with Custom Options
async function customOptionsExample() {
  console.log('📄 Example 2: PDF with Custom Options');
  
  const html = `
    <h1>Custom PDF Document</h1>
    <p>This PDF demonstrates custom formatting options.</p>
    <table border="1" style="width: 100%; border-collapse: collapse;">
      <tr>
        <th style="padding: 10px;">Feature</th>
        <th style="padding: 10px;">Description</th>
      </tr>
      <tr>
        <td style="padding: 10px;">Custom Margins</td>
        <td style="padding: 10px;">1 inch margins on all sides</td>
      </tr>
      <tr>
        <td style="padding: 10px;">Letter Format</td>
        <td style="padding: 10px;">US Letter size instead of A4</td>
      </tr>
      <tr>
        <td style="padding: 10px;">Header/Footer</td>
        <td style="padding: 10px;">Custom header and footer templates</td>
      </tr>
    </table>
  `;
  
  const options = {
    format: 'Letter',
    margin: {
      top: '1in',
      right: '1in',
      bottom: '1in',
      left: '1in'
    },
    displayHeaderFooter: true,
    headerTemplate: `
      <div style="font-size: 10px; text-align: center; width: 100%; margin: 0 20px;">
        <strong>Custom Document Header</strong>
      </div>
    `,
    footerTemplate: `
      <div style="font-size: 10px; text-align: center; width: 100%; margin: 0 20px;">
        Page <span class="pageNumber"></span> of <span class="totalPages"></span>
      </div>
    `
  };
  
  try {
    const pdfBuffer = await pdfGenerator.generatePdfFromHtml(html, options);
    fs.writeFileSync('example-custom.pdf', pdfBuffer);
    console.log('✅ Custom PDF saved as example-custom.pdf');
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Example 3: Professional Resume
async function resumeExample() {
  console.log('👔 Example 3: Professional Resume');
  
  const resumeHtml = `
    <div class="contact-info">
      <h1>Jane Smith</h1>
      <div class="contact-details">
        <span><EMAIL></span>
        <span>+1 (555) 987-6543</span>
        <span>LinkedIn: linkedin.com/in/janesmith</span>
        <span>San Francisco, CA</span>
      </div>
    </div>

    <div class="section">
      <h2>Professional Summary</h2>
      <p>Senior Full-Stack Developer with 8+ years of experience building scalable web applications. 
      Expert in React, Node.js, and cloud technologies with a proven track record of leading 
      high-performing development teams.</p>
    </div>

    <div class="section">
      <h2>Technical Skills</h2>
      <div class="skills-grid">
        <div class="skill-category">
          <h4>Frontend</h4>
          <div class="skill-tags">
            <span class="skill-tag">React</span>
            <span class="skill-tag">Angular</span>
            <span class="skill-tag">Vue.js</span>
            <span class="skill-tag">TypeScript</span>
          </div>
        </div>
        <div class="skill-category">
          <h4>Backend</h4>
          <div class="skill-tags">
            <span class="skill-tag">Node.js</span>
            <span class="skill-tag">Python</span>
            <span class="skill-tag">Java</span>
            <span class="skill-tag">PostgreSQL</span>
          </div>
        </div>
        <div class="skill-category">
          <h4>Cloud</h4>
          <div class="skill-tags">
            <span class="skill-tag">AWS</span>
            <span class="skill-tag">Docker</span>
            <span class="skill-tag">Kubernetes</span>
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>Professional Experience</h2>
      
      <div class="experience-item">
        <div class="item-header">
          <div>
            <div class="item-title">Senior Full-Stack Developer</div>
            <div class="item-company">TechCorp Solutions</div>
            <div class="item-location">San Francisco, CA</div>
          </div>
          <div class="item-date">Mar 2020 - Present</div>
        </div>
        <div class="achievements">
          <ul>
            <li>Led development of microservices architecture serving 2M+ users</li>
            <li>Reduced application load time by 60% through performance optimization</li>
            <li>Mentored team of 5 junior developers and established code review processes</li>
            <li>Implemented CI/CD pipelines reducing deployment time from hours to minutes</li>
          </ul>
        </div>
      </div>

      <div class="experience-item">
        <div class="item-header">
          <div>
            <div class="item-title">Full-Stack Developer</div>
            <div class="item-company">InnovateLab</div>
            <div class="item-location">Palo Alto, CA</div>
          </div>
          <div class="item-date">Jun 2018 - Feb 2020</div>
        </div>
        <div class="achievements">
          <ul>
            <li>Built responsive web applications using React and Node.js</li>
            <li>Designed and implemented RESTful APIs serving mobile and web clients</li>
            <li>Collaborated with UX team to improve user engagement by 40%</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>Education</h2>
      
      <div class="education-item">
        <div class="item-header">
          <div>
            <div class="item-title">Master of Science in Computer Science</div>
            <div class="item-company">Stanford University</div>
            <div class="item-location">Stanford, CA</div>
          </div>
          <div class="item-date">2016 - 2018</div>
        </div>
        <p>Specialization in Artificial Intelligence and Machine Learning</p>
      </div>
    </div>
  `;
  
  try {
    const pdfBuffer = await generateResumePdf(resumeHtml);
    fs.writeFileSync('example-resume.pdf', pdfBuffer);
    console.log('✅ Resume PDF saved as example-resume.pdf');
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Example 4: Invoice/Report Template
async function invoiceExample() {
  console.log('🧾 Example 4: Invoice Template');
  
  const invoiceHtml = `
    <div style="max-width: 800px; margin: 0 auto; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">INVOICE</h1>
        <p style="margin: 5px 0; color: #666;">Invoice #INV-2024-001</p>
      </div>
      
      <div style="display: flex; justify-content: space-between; margin-bottom: 30px;">
        <div>
          <h3>From:</h3>
          <p>Your Company Name<br>
          123 Business St<br>
          City, State 12345<br>
          <EMAIL></p>
        </div>
        <div style="text-align: right;">
          <h3>To:</h3>
          <p>Client Company<br>
          456 Client Ave<br>
          City, State 67890<br>
          <EMAIL></p>
        </div>
      </div>
      
      <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
        <thead>
          <tr style="background-color: #f8f9fa;">
            <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Description</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">Qty</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: right;">Rate</th>
            <th style="border: 1px solid #ddd; padding: 12px; text-align: right;">Amount</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px;">Web Development Services</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">40</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">$100.00</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">$4,000.00</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px;">PDF Generation Integration</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">8</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">$125.00</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">$1,000.00</td>
          </tr>
          <tr style="background-color: #f8f9fa; font-weight: bold;">
            <td colspan="3" style="border: 1px solid #ddd; padding: 12px; text-align: right;">Total:</td>
            <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">$5,000.00</td>
          </tr>
        </tbody>
      </table>
      
      <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 5px;">
        <h4>Payment Terms:</h4>
        <p>Payment is due within 30 days of invoice date. Late payments may incur additional fees.</p>
        <p><strong>Thank you for your business!</strong></p>
      </div>
    </div>
  `;
  
  try {
    const pdfBuffer = await pdfGenerator.generatePdfFromHtml(invoiceHtml);
    fs.writeFileSync('example-invoice.pdf', pdfBuffer);
    console.log('✅ Invoice PDF saved as example-invoice.pdf');
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Run all examples
async function runAllExamples() {
  console.log('🚀 Running PDF Generation Examples\n');
  
  try {
    await basicExample();
    console.log('');
    
    await customOptionsExample();
    console.log('');
    
    await resumeExample();
    console.log('');
    
    await invoiceExample();
    console.log('');
    
    console.log('🎉 All examples completed successfully!');
    console.log('📁 Check the generated PDF files in the current directory.');
    
  } catch (error) {
    console.error('💥 Example execution failed:', error);
  } finally {
    await pdfGenerator.close();
    console.log('🧹 Browser resources cleaned up');
  }
}

// Export functions
module.exports = {
  basicExample,
  customOptionsExample,
  resumeExample,
  invoiceExample,
  runAllExamples
};

// Run examples if this file is executed directly
if (require.main === module) {
  runAllExamples();
}
