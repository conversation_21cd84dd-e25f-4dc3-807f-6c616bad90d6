const puppeteer = require('puppeteer');

async function generatePdfFromHtml(htmlContent, options = {}) {
  let browser = null;
  let page = null;
  
  try {
    browser = await puppeteer.launch({
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ],
      timeout: 60000
    });
    
    page = await browser.newPage();
    
    // Set default options
    const defaultOptions = {
      format: 'A4',
      margin: {
        top: '0.5in',
        right: '0.5in',
        bottom: '0.5in',
        left: '0.5in'
      },
      printBackground: true,
      displayHeaderFooter: false
    };

    const finalOptions = { ...defaultOptions, ...options };

    // Set content and wait for it to load
    await page.setContent(htmlContent, {
      waitUntil: 'domcontentloaded',
      timeout: 30000
    });
    
    // Wait a bit more to ensure everything is loaded
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: finalOptions.format,
      margin: finalOptions.margin,
      printBackground: finalOptions.printBackground,
      displayHeaderFooter: finalOptions.displayHeaderFooter,
      headerTemplate: finalOptions.headerTemplate,
      footerTemplate: finalOptions.footerTemplate
    });

    return Buffer.from(pdfBuffer);
  } catch (error) {
    console.error('Error in generatePdfFromHtml:', error);
    throw error;
  } finally {
    if (page) {
      try {
        await page.close();
      } catch (e) {
        console.error('Error closing page:', e);
      }
    }
    if (browser) {
      try {
        await browser.close();
      } catch (e) {
        console.error('Error closing browser:', e);
      }
    }
  }
}

module.exports = {
  generatePdfFromHtml
};
