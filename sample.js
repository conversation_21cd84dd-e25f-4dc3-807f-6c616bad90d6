const fs = require('fs');
const path = require('path');
const { pdfGenerator, generateResumePdf } = require('./index');

// Sample HTML content for testing
const sampleHtmlContent = `
  <div class="contact-info">
    <h1><PERSON></h1>
    <div class="contact-details">
      <span><EMAIL></span>
      <span>+1 (555) 123-4567</span>
      <span>LinkedIn: linkedin.com/in/johndoe</span>
      <span>New York, NY</span>
    </div>
  </div>

  <div class="section">
    <h2>Professional Summary</h2>
    <p>Experienced software developer with 5+ years of expertise in full-stack development, 
    specializing in JavaScript, React, Node.js, and cloud technologies. Proven track record 
    of delivering scalable web applications and leading cross-functional teams.</p>
  </div>

  <div class="section">
    <h2>Technical Skills</h2>
    <div class="skills-grid">
      <div class="skill-category">
        <h4>Frontend</h4>
        <div class="skill-tags">
          <span class="skill-tag">React</span>
          <span class="skill-tag">Vue.js</span>
          <span class="skill-tag">TypeScript</span>
          <span class="skill-tag">HTML5</span>
          <span class="skill-tag">CSS3</span>
        </div>
      </div>
      <div class="skill-category">
        <h4>Backend</h4>
        <div class="skill-tags">
          <span class="skill-tag">Node.js</span>
          <span class="skill-tag">Express</span>
          <span class="skill-tag">Python</span>
          <span class="skill-tag">PostgreSQL</span>
          <span class="skill-tag">MongoDB</span>
        </div>
      </div>
      <div class="skill-category">
        <h4>Cloud & DevOps</h4>
        <div class="skill-tags">
          <span class="skill-tag">AWS</span>
          <span class="skill-tag">Docker</span>
          <span class="skill-tag">Kubernetes</span>
          <span class="skill-tag">CI/CD</span>
        </div>
      </div>
    </div>
  </div>

  <div class="section">
    <h2>Professional Experience</h2>
    
    <div class="experience-item">
      <div class="item-header">
        <div>
          <div class="item-title">Senior Software Engineer</div>
          <div class="item-company">Tech Solutions Inc.</div>
          <div class="item-location">New York, NY</div>
        </div>
        <div class="item-date">Jan 2021 - Present</div>
      </div>
      <div class="achievements">
        <ul>
          <li>Led development of microservices architecture serving 1M+ users</li>
          <li>Improved application performance by 40% through code optimization</li>
          <li>Mentored 3 junior developers and conducted code reviews</li>
          <li>Implemented CI/CD pipelines reducing deployment time by 60%</li>
        </ul>
      </div>
    </div>

    <div class="experience-item">
      <div class="item-header">
        <div>
          <div class="item-title">Full Stack Developer</div>
          <div class="item-company">StartupXYZ</div>
          <div class="item-location">San Francisco, CA</div>
        </div>
        <div class="item-date">Jun 2019 - Dec 2020</div>
      </div>
      <div class="achievements">
        <ul>
          <li>Built responsive web applications using React and Node.js</li>
          <li>Designed and implemented RESTful APIs</li>
          <li>Collaborated with UX/UI designers to improve user experience</li>
          <li>Reduced page load times by 50% through optimization techniques</li>
        </ul>
      </div>
    </div>
  </div>

  <div class="section">
    <h2>Education</h2>
    
    <div class="education-item">
      <div class="item-header">
        <div>
          <div class="item-title">Bachelor of Science in Computer Science</div>
          <div class="item-company">University of Technology</div>
          <div class="item-location">Boston, MA</div>
        </div>
        <div class="item-date">2015 - 2019</div>
      </div>
      <p>Graduated Magna Cum Laude, GPA: 3.8/4.0</p>
    </div>
  </div>

  <div class="section">
    <h2>Projects</h2>
    
    <div class="experience-item">
      <div class="item-header">
        <div>
          <div class="item-title">E-commerce Platform</div>
          <div class="item-company">Personal Project</div>
        </div>
        <div class="item-date">2023</div>
      </div>
      <div class="achievements">
        <ul>
          <li>Built full-stack e-commerce platform with React and Node.js</li>
          <li>Integrated payment processing with Stripe API</li>
          <li>Implemented user authentication and authorization</li>
          <li>Deployed on AWS with auto-scaling capabilities</li>
        </ul>
      </div>
    </div>
  </div>
`;

// Sample function to generate PDF from HTML
async function generateSamplePdf() {
  try {
    console.log('🚀 Starting PDF generation...');
    
    // Generate PDF using the resume function
    const pdfBuffer = await generateResumePdf(sampleHtmlContent);
    
    // Save the PDF to a file
    const outputPath = path.join(__dirname, 'sample-resume.pdf');
    fs.writeFileSync(outputPath, pdfBuffer);
    
    console.log(`✅ PDF generated successfully: ${outputPath}`);
    console.log(`📄 File size: ${(pdfBuffer.length / 1024).toFixed(2)} KB`);
    
    return outputPath;
  } catch (error) {
    console.error('❌ Error generating PDF:', error);
    throw error;
  }
}

// Sample function to generate PDF from URL
async function generatePdfFromUrl(url) {
  try {
    console.log(`🌐 Generating PDF from URL: ${url}`);
    
    const pdfBuffer = await pdfGenerator.generatePdfFromUrl(url);
    
    // Save the PDF to a file
    const outputPath = path.join(__dirname, 'url-generated.pdf');
    fs.writeFileSync(outputPath, pdfBuffer);
    
    console.log(`✅ PDF from URL generated successfully: ${outputPath}`);
    console.log(`📄 File size: ${(pdfBuffer.length / 1024).toFixed(2)} KB`);
    
    return outputPath;
  } catch (error) {
    console.error('❌ Error generating PDF from URL:', error);
    throw error;
  }
}

// Sample function to generate PDF with custom options
async function generateCustomPdf() {
  try {
    console.log('🎨 Generating PDF with custom options...');
    
    const customHtml = `
      <h1 style="color: #2563eb; text-align: center;">Custom PDF Document</h1>
      <p>This is a sample PDF generated with custom options.</p>
      <h2>Features:</h2>
      <ul>
        <li>Custom margins</li>
        <li>Letter format</li>
        <li>Header and footer</li>
        <li>Background printing enabled</li>
      </ul>
      <div style="background: linear-gradient(45deg, #f0f9ff, #e0f2fe); padding: 20px; margin: 20px 0; border-radius: 8px;">
        <h3>Styled Content Block</h3>
        <p>This block demonstrates background printing capabilities.</p>
      </div>
    `;
    
    const customOptions = {
      format: 'Letter',
      margin: {
        top: '1in',
        right: '0.75in',
        bottom: '1in',
        left: '0.75in'
      },
      printBackground: true,
      displayHeaderFooter: true,
      headerTemplate: '<div style="font-size: 10px; text-align: center; width: 100%;">Custom Header - Generated on <span class="date"></span></div>',
      footerTemplate: '<div style="font-size: 10px; text-align: center; width: 100%;">Page <span class="pageNumber"></span> of <span class="totalPages"></span></div>'
    };
    
    const pdfBuffer = await pdfGenerator.generatePdfFromHtml(customHtml, customOptions);
    
    // Save the PDF to a file
    const outputPath = path.join(__dirname, 'custom-options.pdf');
    fs.writeFileSync(outputPath, pdfBuffer);
    
    console.log(`✅ Custom PDF generated successfully: ${outputPath}`);
    console.log(`📄 File size: ${(pdfBuffer.length / 1024).toFixed(2)} KB`);
    
    return outputPath;
  } catch (error) {
    console.error('❌ Error generating custom PDF:', error);
    throw error;
  }
}

// Main execution function
async function main() {
  try {
    console.log('🎯 PDF Generator Sample Functions\n');

    // Generate sample resume PDF
    await generateSamplePdf();
    console.log('');

    // Generate PDF with custom options
    await generateCustomPdf();
    console.log('');

    // Test simple HTML to PDF
    await testSimpleHtmlToPdf();
    console.log('');

    // Uncomment the line below to test URL-based PDF generation
    // await generatePdfFromUrl('https://example.com');

    console.log('🎉 All PDF generation samples completed successfully!');

  } catch (error) {
    console.error('💥 Sample execution failed:', error);
  } finally {
    // Clean up browser resources
    await pdfGenerator.close();
    console.log('🧹 Browser resources cleaned up');
  }
}

// Simple HTML to PDF test function
async function testSimpleHtmlToPdf() {
  try {
    console.log('📝 Testing simple HTML to PDF conversion...');

    const simpleHtml = `
      <h1 style="color: #2563eb;">Simple HTML to PDF Test</h1>
      <p>This demonstrates basic HTML to PDF conversion.</p>
      <ul>
        <li>Easy to use API</li>
        <li>Customizable options</li>
        <li>High-quality output</li>
      </ul>
      <div style="background: #f0f9ff; padding: 15px; margin: 10px 0; border-radius: 5px;">
        <strong>Note:</strong> This PDF was generated on ${new Date().toLocaleString()}
      </div>
    `;

    const pdfBuffer = await pdfGenerator.generatePdfFromHtml(simpleHtml);

    // Save the PDF to a file
    const outputPath = path.join(__dirname, 'simple-html-test.pdf');
    fs.writeFileSync(outputPath, pdfBuffer);

    console.log(`✅ Simple HTML PDF generated successfully: ${outputPath}`);
    console.log(`📄 File size: ${(pdfBuffer.length / 1024).toFixed(2)} KB`);

    return outputPath;
  } catch (error) {
    console.error('❌ Error generating simple HTML PDF:', error);
    throw error;
  }
}

// Export functions for use in other modules
module.exports = {
  generateSamplePdf,
  generatePdfFromUrl,
  generateCustomPdf,
  testSimpleHtmlToPdf,
  main
};

// Run the main function if this file is executed directly
if (require.main === module) {
  main();
}
