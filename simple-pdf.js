const puppeteer = require('puppeteer');
const fs = require('fs');

async function generatePdfFromHtml(htmlContent, options = {}) {
  let browser = null;
  let page = null;
  
  try {
    console.log('Launching browser...');
    browser = await puppeteer.launch({
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ],
      timeout: 60000
    });
    
    console.log('Creating new page...');
    page = await browser.newPage();
    
    // Set default options
    const defaultOptions = {
      format: 'A4',
      margin: {
        top: '0.5in',
        right: '0.5in',
        bottom: '0.5in',
        left: '0.5in'
      },
      printBackground: true,
      displayHeaderFooter: false
    };

    const finalOptions = { ...defaultOptions, ...options };

    console.log('Setting page content...');
    // Set content and wait for it to load
    await page.setContent(htmlContent, {
      waitUntil: 'domcontentloaded',
      timeout: 30000
    });

    // Wait a bit more to ensure everything is loaded
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('Generating PDF...');
    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: finalOptions.format,
      margin: finalOptions.margin,
      printBackground: finalOptions.printBackground,
      displayHeaderFooter: finalOptions.displayHeaderFooter,
      headerTemplate: finalOptions.headerTemplate,
      footerTemplate: finalOptions.footerTemplate
    });

    console.log('PDF generated successfully');
    return Buffer.from(pdfBuffer);
    
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  } finally {
    if (page) {
      try {
        await page.close();
        console.log('Page closed');
      } catch (e) {
        console.error('Error closing page:', e);
      }
    }
    if (browser) {
      try {
        await browser.close();
        console.log('Browser closed');
      } catch (e) {
        console.error('Error closing browser:', e);
      }
    }
  }
}

async function testSimplePdf() {
  try {
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 20px;
            color: #333;
          }
          h1 {
            color: #2563eb;
            text-align: center;
          }
          .content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
          }
        </style>
      </head>
      <body>
        <h1>Test PDF Document</h1>
        <div class="content">
          <h2>Hello World!</h2>
          <p>This is a test PDF generated using Puppeteer and Node.js.</p>
          <ul>
            <li>Feature 1: HTML to PDF conversion</li>
            <li>Feature 2: Custom styling support</li>
            <li>Feature 3: Configurable options</li>
          </ul>
        </div>
        <p>Generated on: ${new Date().toLocaleString()}</p>
      </body>
      </html>
    `;
    
    console.log('Starting PDF generation test...');
    const pdfBuffer = await generatePdfFromHtml(htmlContent);
    
    // Save the PDF
    const filename = 'simple-test.pdf';
    fs.writeFileSync(filename, pdfBuffer);
    
    console.log(`✅ PDF saved as ${filename}`);
    console.log(`📄 File size: ${(pdfBuffer.length / 1024).toFixed(2)} KB`);
    
    return filename;
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Export the function
module.exports = {
  generatePdfFromHtml,
  testSimplePdf
};

// Run test if this file is executed directly
if (require.main === module) {
  testSimplePdf()
    .then((filename) => {
      console.log(`🎉 Test completed successfully! PDF saved as: ${filename}`);
    })
    .catch((error) => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}
