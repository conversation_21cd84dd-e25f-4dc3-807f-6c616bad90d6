console.log('Test script starting...');

const { pdfGenerator } = require('./index');

async function simpleTest() {
  console.log('Starting simple PDF test...');

  try {
    const simpleHtml = '<h1>Hello World</h1><p>This is a test PDF.</p>';
    console.log('Generating PDF...');

    const pdfBuffer = await pdfGenerator.generatePdfFromHtml(simpleHtml);
    console.log('PDF generated, buffer length:', pdfBuffer.length);

    const fs = require('fs');
    fs.writeFileSync('test.pdf', pdfBuffer);
    console.log('PDF saved as test.pdf');

  } catch (error) {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    try {
      await pdfGenerator.close();
      console.log('Browser closed');
    } catch (closeError) {
      console.error('Error closing browser:', closeError.message);
    }
  }
}

console.log('About to call simpleTest...');
simpleTest().then(() => {
  console.log('Test completed');
}).catch((error) => {
  console.error('Unhandled error:', error);
});
